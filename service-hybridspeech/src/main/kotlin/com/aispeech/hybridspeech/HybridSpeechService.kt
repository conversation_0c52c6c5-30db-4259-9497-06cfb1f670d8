package com.aispeech.hybridspeech

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.IBinder
import android.os.RemoteCallbackList
import androidx.core.app.NotificationCompat
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.hybridspeech.core.MainAppController
import com.aispeech.hybridspeech.core.safeCollect
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * 混合语音识别服务
 * 通过AIDL对外提供语音识别服务
 */
class HybridSpeechService : Service() {
  companion object {
    private const val TAG = "HybridSpeechService"
    private const val NOTIFICATION_ID = 1001
    private const val CHANNEL_ID = "hybrid_speech_channel"
  }

  private lateinit var mainController: MainAppController
  private val callbacks = RemoteCallbackList<ITranscriptionCallback>()
  private val progressCallbacks = RemoteCallbackList<IRecordProgressCallback>()

  private val moduleScopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = "HybridSpeechService-${this.hashCode()}",
    parentScope = CoroutineScopeManager.getMainParentScope()
  )
  private val serviceScope: CoroutineScope = moduleScopeDelegate

  private var resultSubscriptionJob: Job? = null
  private var statusSubscriptionJob: Job? = null
  private var errorSubscriptionJob: Job? = null

  // 配置提供者
  private var configProvider: IHybridSpeechConfigProvider? = null

  private val binder = object : IHybridSpeechService.Stub() {

    override fun startRecordingWithConfigAsync(config: RecordingConfig?, callback: IStartRecordingCallback?) {
      if (config != null && callback != null) {
        mainController.startRecordingWithConfigAsync(config, callback)
      } else {
        AILog.e(TAG, "Recording config or callback is null")
        callback?.onStartRecordingError(
          StartRecordingErrorCode.INVALID_CONFIG,
          "Recording config or callback is null"
        )
      }
    }

    override fun pauseRecordingAsync(callback: IPauseRecordingCallback?) {
      if (callback != null) {
        mainController.pauseRecordingAsync(callback)
      } else {
        AILog.e(TAG, "Pause recording callback is null")
      }
    }

    override fun resumeRecordingAsync(callback: IResumeRecordingCallback?) {
      if (callback != null) {
        mainController.resumeRecordingAsync(callback)
      } else {
        AILog.e(TAG, "Resume recording callback is null")
      }
    }

    override fun stopRecordingWithResultAsync(callback: IStopRecordingCallback?) {
      if (callback != null) {
        mainController.stopRecordingWithResultAsync(callback)
      } else {
        AILog.e(TAG, "Stop recording callback is null")
      }
    }


    override fun registerCallback(callback: ITranscriptionCallback?) {
      if (callback != null) {
        callbacks.register(callback)
      }
    }

    override fun unregisterCallback(callback: ITranscriptionCallback?) {
      if (callback != null) {
        callbacks.unregister(callback)
      }
    }

    override fun getCurrentStatus(): Int {
      return mainController.getCurrentStatus()
    }

    override fun getRecordingDuration(): Long {
      return mainController.getRecordingDuration()
    }

    override fun registerProgressCallback(callback: IRecordProgressCallback?, intervalMs: Int) {
      if (callback != null) {
        progressCallbacks.register(callback)
        mainController.registerProgressCallback(callback, intervalMs)
      }
    }

    override fun unregisterProgressCallback(callback: IRecordProgressCallback?) {
      if (callback != null) {
        progressCallbacks.unregister(callback)
        mainController.unregisterProgressCallback(callback)
      }
    }

    override fun unregisterAllProgressCallbacks() {
      progressCallbacks.kill()
      mainController.unregisterAllProgressCallbacks()
    }

    override fun registerConfigProvider(provider: IHybridSpeechConfigProvider?) {
      configProvider = provider
    }

    override fun unregisterConfigProvider() {
      configProvider = null
    }

    /**
     * 在线录音
     */
    override fun startRecordingWithProvider(config: RecordingConfig?, callback: IStartRecordingCallback) {
      AILog.d(TAG, "startRecordingWithProvider called - config: $config, callback: $callback")
      AILog.d(TAG, "Caller PID: ${android.os.Binder.getCallingPid()}, UID: ${android.os.Binder.getCallingUid()}")

      if (config == null) {
        AILog.e(TAG, "Config or callback is null - config: $config")
        callback.onStartRecordingError(
          StartRecordingErrorCode.INVALID_CONFIG,
          "Config or callback is null"
        )
        return
      }

      val provider = configProvider
      if (provider == null) {
        AILog.e(TAG, "Config provider not registered")
        callback.onStartRecordingError(
          StartRecordingErrorCode.INVALID_CONFIG,
          "Config provider not registered"
        )
        return
      }

      mainController.startRecordingWithProvider(config, provider, callback)
    }
  }

  @SuppressLint("InlinedApi")
  override fun onCreate() {
    super.onCreate()
    AILog.i(TAG, "Service onCreate")

    try {
      mainController = MainAppController(this)
      createNotificationChannel()

      val filter = IntentFilter(Intent.ACTION_USER_PRESENT)
      registerReceiver(userPresentReceiver, filter)
      AILog.i(TAG, "已动态注册 USER_PRESENT 解锁广播")

      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        startForeground(
          NOTIFICATION_ID,
          createNotification(),
          android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE
        )
      } else {
        startForeground(NOTIFICATION_ID, createNotification())
      }

      subscribeToEvents()
      AILog.i(TAG, "Service onCreate completed")
    } catch (e: Exception) {
      AILog.e(TAG, "Service onCreate failed", e)
      throw e
    }
  }

  override fun onBind(intent: Intent?): IBinder {
    AILog.i(TAG, "Service onBind")
    return binder
  }

  override fun onUnbind(intent: Intent?): Boolean {
    AILog.i(TAG, "Service onUnbind")
    return super.onUnbind(intent)
  }

  override fun onDestroy() {
    AILog.i(TAG, "Service onDestroy")

    resultSubscriptionJob?.cancel()
    statusSubscriptionJob?.cancel()
    errorSubscriptionJob?.cancel()

    mainController.release()

    callbacks.kill()
    progressCallbacks.kill()

    unregisterReceiver(userPresentReceiver)
    AILog.i(TAG, "已注销 USER_PRESENT 解锁广播")

    super.onDestroy()
  }

  /**
   * 订阅事件
   */
  private fun subscribeToEvents() {
    // 订阅转写结果
    resultSubscriptionJob = serviceScope.launch {
      mainController.transcriptionResultFlow.safeCollect(
        tag = TAG,
        onError = { error ->
          AILog.e(TAG, "Error in transcription result flow", error)
        }
      ) { result ->
        notifyTranscriptionResult(result)
      }
    }

    // 订阅状态变化
    statusSubscriptionJob = serviceScope.launch {
      mainController.statusChangeFlow.safeCollect(
        tag = TAG,
        onError = { error ->
          AILog.e(TAG, "Error in status change flow", error)
        }
      ) { status ->
        notifyStatusChanged(status)
        updateNotification(status)
      }
    }

    // 订阅错误事件
    errorSubscriptionJob = serviceScope.launch {
      mainController.errorFlow.safeCollect(
        tag = TAG,
        onError = { error ->
          AILog.e(TAG, "Error in error flow", error)
        }
      ) { error ->
        notifyError(error)
      }
    }
  }

  /**
   * 通知转写结果
   */
  private fun notifyTranscriptionResult(result: TranscriptionResult) {
    val callbackCount = callbacks.beginBroadcast()
    try {
      for (i in 0 until callbackCount) {
        try {
          callbacks.getBroadcastItem(i)?.onTranscriptionResult(result)
        } catch (e: Exception) {
          AILog.e(TAG, "Error notifying transcription result to callback $i", e)
        }
      }
    } finally {
      callbacks.finishBroadcast()
    }
  }

  /**
   * 通知状态变化
   */
  private fun notifyStatusChanged(status: Int) {
    val callbackCount = callbacks.beginBroadcast()
    try {
      for (i in 0 until callbackCount) {
        try {
          callbacks.getBroadcastItem(i)?.onStatusChanged(status)
        } catch (e: Exception) {
          AILog.e(TAG, "Error notifying status change to callback $i", e)
        }
      }
    } finally {
      callbacks.finishBroadcast()
    }
  }

  /**
   * 通知错误
   */
  private fun notifyError(error: String) {
    val callbackCount = callbacks.beginBroadcast()
    try {
      for (i in 0 until callbackCount) {
        try {
          callbacks.getBroadcastItem(i)?.onError(error)
        } catch (e: Exception) {
          AILog.e(TAG, "Error notifying error to callback $i", e)
        }
      }
    } finally {
      callbacks.finishBroadcast()
    }
  }

  /**
   * 创建通知渠道
   */
  private fun createNotificationChannel() {
    val channel = NotificationChannel(
      CHANNEL_ID,
      "Hybrid Speech Service",
      NotificationManager.IMPORTANCE_LOW
    ).apply {
      description = "Hybrid Speech Recognition Service"
      setShowBadge(false)
    }

    val notificationManager = getSystemService(NotificationManager::class.java)
    notificationManager.createNotificationChannel(channel)
  }

  /**
   * 创建通知
   */
  private fun createNotification(status: Int = ServiceStatus.IDLE): Notification {
    val statusText = when (status) {
      ServiceStatus.IDLE -> "待机中"
      ServiceStatus.RECORDING -> "录音中"
      ServiceStatus.PROCESSING -> "处理中"
      ServiceStatus.ERROR -> "错误"
      ServiceStatus.INITIALIZING -> "初始化中"
      ServiceStatus.PAUSED -> "已暂停"
      else -> "未知状态"
    }

    return NotificationCompat.Builder(this, CHANNEL_ID)
      .setContentTitle("混合语音识别服务")
      .setContentText("状态: $statusText")
      .setSmallIcon(android.R.drawable.ic_btn_speak_now) // 使用系统内置图标
      .setOngoing(true)
      .setShowWhen(false)
      .build()
  }

  /**
   * 更新通知
   */
  private fun updateNotification(status: Int) {
    val notification = createNotification(status)
    val notificationManager = getSystemService(NotificationManager::class.java)
    notificationManager.notify(NOTIFICATION_ID, notification)
  }


  private val userPresentReceiver = object : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
      if (intent?.action == Intent.ACTION_USER_PRESENT) {
        AILog.i(TAG, "收到用户解锁广播，HybridSpeechService 可自保活或执行自定义操作")
        try {
          context?.startForegroundService(Intent(context, HybridSpeechService::class.java))
        } catch (e: Exception) { 
          AILog.e(TAG, "启动 HybridSpeechService 失败", e)
        }
      }
    }
  }
}
