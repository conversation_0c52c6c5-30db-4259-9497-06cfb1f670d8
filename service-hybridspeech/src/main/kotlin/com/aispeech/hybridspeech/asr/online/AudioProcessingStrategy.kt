package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.AsrResumeConfig
import com.aispeech.hybridspeech.Mp3EncodingConfig
import com.aispeech.hybridspeech.OpusEncodingConfig
import com.aispeech.hybridspeech.storage.RecordingStorageManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch

/**
 * 音频处理配置基类
 * 统一不同音频格式的配置接口
 */
sealed class AudioProcessingConfig {
  /**
   * Opus音频处理配置
   */
  data class OpusConfig(
    val opusEncodingConfig: OpusEncodingConfig
  ) : AudioProcessingConfig()

  /**
   * MP3音频处理配置
   */
  data class Mp3Config(
    val mp3EncodingConfig: Mp3EncodingConfig
  ) : AudioProcessingConfig()
}

/**
 * 音频类型枚举
 */
enum class AudioType(val value: String) {
  OGG_OPUS("ogg_opus"),
  MP3("mp3");

  companion object {
    fun fromString(value: String): AudioType {
      return when (value.lowercase()) {
        "ogg_opus" -> OGG_OPUS
        "mp3" -> MP3
        else -> OGG_OPUS // 默认使用 OGG_OPUS
      }
    }
  }
}

/**
 * 音频处理策略接口
 * 定义统一的音频处理操作，屏蔽底层实现差异
 */
interface AudioProcessingStrategy {

  /**
   * 启动音频处理
   * @param dataFlow 音频数据流（PCM或MP3）
   * @param config 音频处理配置
   * @param asrSocketClient ASR Socket客户端
   * @param scope 协程作用域
   * @return 是否启动成功
   */
  fun startProcessing(
    dataFlow: SharedFlow<ByteArray>,
    config: AudioProcessingConfig,
    asrSocketClient: IAsrSocketClient,
    scope: CoroutineScope
  ): Boolean

  /**
   * 停止音频处理
   */
  suspend fun stopProcessing()

  /**
   * 重置处理器状态
   */
  fun reset()

  /**
   * 启动续传处理
   * @param resumeConfig 续传配置
   * @param storageManager 存储管理器
   * @param asrSocketClient ASR Socket客户端
   * @param scope 协程作用域
   * @param onResumeCompleted 续传完成回调
   */
  fun startResumeTransmission(
    resumeConfig: AsrResumeConfig,
    storageManager: RecordingStorageManager,
    asrSocketClient: IAsrSocketClient,
    scope: CoroutineScope,
    onResumeCompleted: () -> Unit
  )

  /**
   * 停止续传处理
   */
  suspend fun stopResumeTransmission()

  /**
   * 暂停实时处理（续传期间）
   */
  fun pauseRealTimeProcessing()

  /**
   * 恢复实时处理
   */
  fun resumeRealTimeProcessing()

  /**
   * 检查是否支持续传
   * @param resumeConfig 续传配置
   * @return 是否支持续传
   */
  fun supportsResume(resumeConfig: AsrResumeConfig): Boolean

  /**
   * 获取处理器状态信息
   */
  fun getStatus(): AudioProcessingStatus

  /**
   * 关闭处理器
   */
  fun shutdown(reason: String)
}

/**
 * 音频处理状态
 */
data class AudioProcessingStatus(
  val isProcessing: Boolean,
  val isResuming: Boolean,
  val isPausedRealTime: Boolean,
  val processingJobActive: Boolean,
  val resumeJobActive: Boolean,
  val additionalInfo: Map<String, Any> = emptyMap()
)

/**
 * 音频处理策略工厂
 * 负责根据音频类型创建相应的策略实例
 */
object AudioProcessingStrategyFactory {

  /**
   * 创建音频处理策略
   * @param audioType 音频类型
   * @return 对应的策略实例
   */
  fun createStrategy(audioType: AudioType): AudioProcessingStrategy {
    return when (audioType) {
      AudioType.OGG_OPUS -> OpusProcessingStrategy()
      AudioType.MP3 -> Mp3ProcessingStrategy()
    }
  }

  /**
   * 创建默认配置
   * @param audioType 音频类型
   * @return 对应的默认配置
   */
  fun createDefaultConfig(audioType: AudioType): AudioProcessingConfig {
    return when (audioType) {
      AudioType.OGG_OPUS -> AudioProcessingConfig.OpusConfig(
        OpusEncodingConfig.createDefault()
      )
      AudioType.MP3 -> AudioProcessingConfig.Mp3Config(
        Mp3EncodingConfig.createDefault()
      )
    }
  }
}

/**
 * Opus音频处理策略
 * 处理PCM -> Opus编码 -> OGG数据流
 */
class OpusProcessingStrategy : AudioProcessingStrategy {
  companion object {
    private const val TAG = "OpusProcessingStrategy"
    private const val STATE_SWITCH_DELAY_MS = 200L
  }

  private val opusEncoder = OpusEncoder()
  private var pcmProcessingJob: Job? = null
  private var oggProcessingJob: Job? = null
  private var resumeProcessingJob: Job? = null

  @Volatile
  private var isProcessing = false
  @Volatile
  private var isResuming = false
  @Volatile
  private var pauseRealTimeProcessing = false

  override fun startProcessing(
    dataFlow: SharedFlow<ByteArray>,
    config: AudioProcessingConfig,
    asrSocketClient: IAsrSocketClient,
    scope: CoroutineScope
  ): Boolean {
    return try {
      if (isProcessing) {
        AILog.w(TAG, "Already processing")
        return true
      }

      // 获取Opus配置
      val opusConfig = when (config) {
        is AudioProcessingConfig.OpusConfig -> config.opusEncodingConfig
        else -> {
          AILog.e(TAG, "Invalid config type for Opus strategy: ${config::class.simpleName}")
          return false
        }
      }

      // 启动Opus编码器（使用配置）
      if (!opusEncoder.startEncodingWithConfig(opusConfig)) {
        AILog.e(TAG, "Failed to start Opus encoder with config")
        return false
      }

      isProcessing = true

      // 处理PCM数据流
      pcmProcessingJob = scope.launch {
        dataFlow.collect { pcmData ->
          try {
            // 检查是否暂停实时处理（续传期间）
            if (pauseRealTimeProcessing) {
              return@collect
            }

            // 将PCM数据送入Opus编码器
            opusEncoder.encodePcmData(pcmData)
          } catch (e: Exception) {
            AILog.e(TAG, "Error processing PCM data", e)
          }
        }
      }

      // 处理OGG数据流
      oggProcessingJob = scope.launch {
        opusEncoder.oggDataFlow.collect { oggData ->
          try {
            // 直接发送 OGG 数据到 ASR 服务
            asrSocketClient.sendAudioData(oggData)
          } catch (e: Exception) {
            AILog.e(TAG, "Error processing OGG data", e)
          }
        }
      }

      AILog.i(TAG, "Opus processing started successfully with config")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Error starting Opus processing", e)
      false
    }
  }



  override suspend fun stopProcessing() {
    isProcessing = false

    pcmProcessingJob?.cancelAndJoin()
    pcmProcessingJob = null

    oggProcessingJob?.cancelAndJoin()
    oggProcessingJob = null

    // 停止编码器
    opusEncoder.stopEncoding()

    AILog.i(TAG, "Opus processing stopped")
  }

  override fun reset() {
    opusEncoder.reset()
    pauseRealTimeProcessing = false
    AILog.i(TAG, "Opus processing reset")
  }

  override fun startResumeTransmission(
    resumeConfig: AsrResumeConfig,
    storageManager: RecordingStorageManager,
    asrSocketClient: IAsrSocketClient,
    scope: CoroutineScope,
    onResumeCompleted: () -> Unit
  ) {
    AILog.i(TAG, "Starting Opus resume from ${resumeConfig.resumeFromOffset}ms")

    try {
      isResuming = true
      pauseRealTimeProcessing = true

      // 重置编码器状态
      opusEncoder.reset()

      // 获取当前PCM文件路径
      val currentPcmFilePath = storageManager.getCurrentPcmFilePath()
      if (currentPcmFilePath == null) {
        AILog.w(TAG, "No PCM file for resume")
        isResuming = false
        pauseRealTimeProcessing = false
        return
      }

      // 创建从指定偏移量开始的PCM数据流
      val resumePcmFlow = storageManager.createPcmDataStreamFromOffset(
        currentPcmFilePath,
        resumeConfig.resumeFromOffset,
        200L
      )

      if (resumePcmFlow == null) {
        AILog.e(TAG, "Failed to create resume PCM flow")
        isResuming = false
        pauseRealTimeProcessing = false
        return
      }

      // 启动续传数据处理
      resumeProcessingJob = scope.launch {
        try {
          resumePcmFlow.collect { pcmData ->
            if (!isResuming) {
              return@collect
            }

            // 将PCM数据送入编码器
            opusEncoder.encodePcmData(pcmData)
          }

          // 续传完成，切换回实时流
          AILog.i(TAG, "Opus resume completed, switching to realtime")
          isResuming = false

          // 快速切换回实时处理
          delay(STATE_SWITCH_DELAY_MS)
          pauseRealTimeProcessing = false

          onResumeCompleted()

        } catch (e: Exception) {
          AILog.e(TAG, "Opus resume transmission error", e)
          isResuming = false
          pauseRealTimeProcessing = false
        }
      }

    } catch (e: Exception) {
      AILog.e(TAG, "Failed to start Opus resume transmission", e)
      isResuming = false
      pauseRealTimeProcessing = false
    }
  }

  override suspend fun stopResumeTransmission() {
    isResuming = false
    resumeProcessingJob?.cancelAndJoin()
    resumeProcessingJob = null
    pauseRealTimeProcessing = false
    AILog.i(TAG, "Opus resume transmission stopped")
  }

  override fun pauseRealTimeProcessing() {
    pauseRealTimeProcessing = true
  }

  override fun resumeRealTimeProcessing() {
    pauseRealTimeProcessing = false
  }

  override fun supportsResume(resumeConfig: AsrResumeConfig): Boolean {
    // Opus模式支持基于时间偏移的续传
    return resumeConfig.resumeFromOffset >= 0
  }

  override fun getStatus(): AudioProcessingStatus {
    return AudioProcessingStatus(
      isProcessing = isProcessing,
      isResuming = isResuming,
      isPausedRealTime = pauseRealTimeProcessing,
      processingJobActive = pcmProcessingJob?.isActive == true || oggProcessingJob?.isActive == true,
      resumeJobActive = resumeProcessingJob?.isActive == true,
      additionalInfo = mapOf(
        "encoderStatus" to "active"
      )
    )
  }

  override fun shutdown(reason: String) {
    opusEncoder.shutdown("$reason - OpusProcessingStrategy")
    AILog.i(TAG, "Opus processing strategy shut down: $reason")
  }
}

/**
 * MP3音频处理策略
 * 直接处理MP3数据流，支持chunk跟踪和续传
 */
class Mp3ProcessingStrategy : AudioProcessingStrategy {
  companion object {
    private const val TAG = "Mp3ProcessingStrategy"
    private const val STATE_SWITCH_DELAY_MS = 200L
  }

  private val mp3ChunkTracker = Mp3ChunkTracker()
  private var mp3ProcessingJob: Job? = null
  private var resumeProcessingJob: Job? = null

  @Volatile
  private var isProcessing = false
  @Volatile
  private var isResuming = false
  @Volatile
  private var pauseRealTimeProcessing = false

  override fun startProcessing(
    dataFlow: SharedFlow<ByteArray>,
    config: AudioProcessingConfig,
    asrSocketClient: IAsrSocketClient,
    scope: CoroutineScope
  ): Boolean {
    return try {
      if (isProcessing) {
        AILog.w(TAG, "Already processing")
        return true
      }

      // 获取MP3配置（用于日志记录）
      val mp3Config = when (config) {
        is AudioProcessingConfig.Mp3Config -> config.mp3EncodingConfig
        else -> {
          AILog.e(TAG, "Invalid config type for MP3 strategy: ${config::class.simpleName}")
          return false
        }
      }

      AILog.i(TAG, "Starting MP3 processing with config: bitRate=${mp3Config.bitRate}, quality=${mp3Config.quality}")

      isProcessing = true

      // 重置 MP3 chunk 跟踪器
      mp3ChunkTracker.reset()

      // 处理 MP3 数据流
      mp3ProcessingJob = scope.launch {
        dataFlow.collect { mp3Data ->
          try {
            // 检查是否暂停实时处理（续传期间）
            if (pauseRealTimeProcessing) {
              return@collect
            }

            // 记录 MP3 chunk 并获取索引
            val chunkIndex = mp3ChunkTracker.recordChunk(mp3Data)

            // 直接发送 MP3 数据到 ASR 服务
            asrSocketClient.sendAudioData(mp3Data)

          } catch (e: Exception) {
            AILog.e(TAG, "Error processing MP3 data", e)
          }
        }
      }

      AILog.i(TAG, "MP3 processing started successfully")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Error starting MP3 processing", e)
      false
    }
  }



  override suspend fun stopProcessing() {
    isProcessing = false

    mp3ProcessingJob?.cancelAndJoin()
    mp3ProcessingJob = null

    // 重置 MP3 chunk 跟踪器
    mp3ChunkTracker.reset()

    AILog.i(TAG, "MP3 processing stopped")
  }

  override fun reset() {
    mp3ChunkTracker.reset()
    pauseRealTimeProcessing = false
    AILog.i(TAG, "MP3 processing reset")
  }

  override fun startResumeTransmission(
    resumeConfig: AsrResumeConfig,
    storageManager: RecordingStorageManager,
    asrSocketClient: IAsrSocketClient,
    scope: CoroutineScope,
    onResumeCompleted: () -> Unit
  ) {
    val chunkIndex = resumeConfig.resumeFromMp3ChunkIndex ?: return

    AILog.i(TAG, "Starting MP3 resume from chunk index: $chunkIndex")

    try {
      isResuming = true
      pauseRealTimeProcessing = true

      // 获取当前 MP3 文件路径
      val currentMp3FilePath = storageManager.getCurrentMp3FilePath()
      if (currentMp3FilePath == null) {
        AILog.w(TAG, "No MP3 file for resume")
        isResuming = false
        pauseRealTimeProcessing = false
        return
      }

      // 获取 chunk 大小信息
      val chunkSizes = mp3ChunkTracker.getChunkSizesFromIndex(0)
      if (chunkSizes.isEmpty()) {
        AILog.w(TAG, "No chunk size information for resume")
        isResuming = false
        pauseRealTimeProcessing = false
        return
      }

      // 创建从指定 chunk 索引开始的 MP3 数据流
      val resumeMp3Flow = storageManager.createMp3DataStreamFromChunkIndex(
        currentMp3FilePath,
        chunkIndex,
        chunkSizes,
        1024
      )

      if (resumeMp3Flow == null) {
        AILog.e(TAG, "Failed to create resume MP3 flow")
        isResuming = false
        pauseRealTimeProcessing = false
        return
      }

      // 启动续传数据处理
      resumeProcessingJob = scope.launch {
        try {
          resumeMp3Flow.collect { mp3Data ->
            if (!isResuming) {
              return@collect
            }

            // 直接发送 MP3 数据到 ASR 服务
            asrSocketClient.sendAudioData(mp3Data)
          }

          // 续传完成，切换回实时流
          AILog.i(TAG, "MP3 resume completed, switching to realtime")
          isResuming = false

          // 快速切换回实时处理
          delay(STATE_SWITCH_DELAY_MS)
          pauseRealTimeProcessing = false

          onResumeCompleted()

        } catch (e: Exception) {
          AILog.e(TAG, "MP3 resume transmission error", e)
          isResuming = false
          pauseRealTimeProcessing = false
        }
      }

    } catch (e: Exception) {
      AILog.e(TAG, "Failed to start MP3 resume transmission", e)
      isResuming = false
      pauseRealTimeProcessing = false
    }
  }

  override suspend fun stopResumeTransmission() {
    isResuming = false
    resumeProcessingJob?.cancelAndJoin()
    resumeProcessingJob = null
    pauseRealTimeProcessing = false
    AILog.i(TAG, "MP3 resume transmission stopped")
  }

  override fun pauseRealTimeProcessing() {
    pauseRealTimeProcessing = true
  }

  override fun resumeRealTimeProcessing() {
    pauseRealTimeProcessing = false
  }

  override fun supportsResume(resumeConfig: AsrResumeConfig): Boolean {
    return resumeConfig.resumeFromMp3ChunkIndex != null && resumeConfig.resumeFromMp3ChunkIndex!! >= 0
  }

  override fun getStatus(): AudioProcessingStatus {
    return AudioProcessingStatus(
      isProcessing = isProcessing,
      isResuming = isResuming,
      isPausedRealTime = pauseRealTimeProcessing,
      processingJobActive = mp3ProcessingJob?.isActive == true,
      resumeJobActive = resumeProcessingJob?.isActive == true,
      additionalInfo = mapOf(
        "chunkCount" to mp3ChunkTracker.getChunkCount(),
        "totalBytes" to mp3ChunkTracker.getTotalBytes()
      )
    )
  }

  /**
   * 获取 MP3 chunk 跟踪状态
   */
  fun getMp3ChunkTrackerStatus(): Mp3ChunkTrackerStatus {
    return mp3ChunkTracker.getStatus()
  }

  override fun shutdown(reason: String) {
    // MP3处理策略没有需要关闭的资源
    AILog.i(TAG, "MP3 processing strategy shut down: $reason")
  }
}
