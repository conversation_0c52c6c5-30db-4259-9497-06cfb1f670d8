package com.aispeech.tablet.feature.meeting.utils

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import com.aispeech.aibase.AILog

object PackageUnstoppedHelper {
  private const val TAG = "PackageUnstoppedHelper"

  /**
   * 通过启动目标包的透明 Activity 激活包
   * @param context 当前 context（建议用 applicationContext）
   * @param packageName 目标包名
   * @param activityName 目标透明 Activity 全类名（如 com.aispeech.hybridspeech.UnfreezeActivity）
   * @return true 成功 false 失败
   */
  fun activateByActivity(
    context: Context,
    packageName: String,
    activityName: String = "$packageName.UnfreezeActivity"
  ): Bo<PERSON>an {
    return try {
      val intent = Intent().apply {
        component = ComponentName(packageName, activityName)
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      }
      context.startActivity(intent)
      AILog.i(TAG, "已通过 Activity 激活 $packageName")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "通过 Activity 激活 $packageName 失败, exception=${e.message}")
      false
    }
  }
}

//  /**
//   * 自动用 shell 执行 cmd package clear-stopped-state 解冻指定包
//   * 需要有 system 或 shell 权限
//   * @param packageName 要解冻的包名
//   * @return true 表示执行成功，false 表示失败
//   */
//  fun clearStoppedState(packageName: String): Boolean {
//    if (packageName.isBlank()) {
//      android.util.Log.e(TAG, "包名不能为空！")
//      return false
//    }
//    try {
//      val cmd = arrayOf("sh", "-c", "cmd package clear-stopped-state $packageName")
//      val process = Runtime.getRuntime().exec(cmd)
//      val exitCode = process.waitFor()
//      if (exitCode == 0) {
//        android.util.Log.i(TAG, "已成功清除 $packageName 的 stopped 状态！")
//        return true
//      } else {
//        val error = process.errorStream.bufferedReader().use { it.readText() }
//        android.util.Log.e(TAG, "执行失败，exitCode=$exitCode，错误信息：$error")
//        return false
//      }
//    } catch (e: Exception) {
//      android.util.Log.e(TAG, "自动解冻包时发生异常", e)
//      return false
//    }
//  }
