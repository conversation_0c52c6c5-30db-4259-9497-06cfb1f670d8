package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import okio.ByteString.Companion.toByteString
import java.util.concurrent.TimeUnit

/*
 * 音频数据源类型枚举已简化
 * 原来的 OGG_CACHE 模式已被移除，统一使用 DIRECT 模式
 * 所有音频数据（OGG Opus 和 MP3）都通过 sendAudioData() 直接发送
 */

/**
 * ASR WebSocket客户端接口
 * 定义统一的WebSocket客户端接口，支持不同的实现方式
 */
interface IAsrSocketClient {
  val transcriptionResultFlow: SharedFlow<String>
  val connectionStatusFlow: SharedFlow<ConnectionStatus>

  fun setConfig(serverUrl: String)
  fun connect()
  fun disconnect()
  fun sendAudioData(audioData: ByteArray): Boolean
  fun getCurrentState(): ConnectionStatus
  fun getCurrentServerUrl(): String
  fun destroy()

  // 用于需要等待完成的场景
  suspend fun disconnectAndJoin()
  suspend fun destroyAndJoin()
}

/**
 * ASR WebSocket客户端工厂
 * 用于创建不同实现的客户端
 */
object AsrSocketClientFactory {
  enum class ClientType {
    OKHTTP,
    KTOR
  }

  fun create(type: ClientType = ClientType.OKHTTP): IAsrSocketClient {
    return when (type) {
      ClientType.OKHTTP -> AsrSocketClientOkHttp()
      else -> throw UnsupportedOperationException()
//      ClientType.KTOR -> AsrSocketClientKtor()
    }
  }
}

/**
 * ASR WebSocket客户端 (OkHttp实现)
 * 统一使用直接发送模式，不再使用应用层缓存
 * 信任 OkHttp 内部的队列管理和背压处理机制
 */
class AsrSocketClientOkHttp : IAsrSocketClient, CoroutineScope {
  companion object {
    private const val TAG = "AsrSocketClientOkHttp"
    private const val CONNECT_TIMEOUT = 10L // 秒
    private const val READ_TIMEOUT = 30L // 秒
    private const val WRITE_TIMEOUT = 10L // 秒
    private const val PING_INTERVAL = 30L // 秒
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  // --- Flows for communication ---
  private val _transcriptionResultFlow = MutableSharedFlow<String>(replay = 0, extraBufferCapacity = 100)
  override val transcriptionResultFlow: SharedFlow<String> = _transcriptionResultFlow.asSharedFlow()

  private val _connectionStatusFlow = MutableSharedFlow<ConnectionStatus>(replay = 1, extraBufferCapacity = 10)
  override val connectionStatusFlow: SharedFlow<ConnectionStatus> = _connectionStatusFlow.asSharedFlow()

  // --- State Management & Concurrency ---
  private val stateLock = Any()
  @Volatile
  private var state: ConnectionStatus = ConnectionStatus.DISCONNECTED

  // --- Configuration & Resources ---
  private var serverUrl: String = ""
  private var webSocket: WebSocket? = null
  private var okHttpClient: OkHttpClient? = null

  override fun setConfig(serverUrl: String) {
    this.serverUrl = serverUrl
    AILog.i(TAG, "ASR config updated: $serverUrl")
  }

  override fun sendAudioData(audioData: ByteArray): Boolean {
    val success = webSocket?.send(audioData.toByteString()) ?: false
    return success
  }

  override fun getCurrentState(): ConnectionStatus = state

  override fun getCurrentServerUrl(): String = serverUrl

  override fun connect() {
    synchronized(stateLock) {
      if (state == ConnectionStatus.CONNECTING || state == ConnectionStatus.CONNECTED) {
        AILog.w(TAG, "Already connecting or connected. Current state: $state")
        return
      }

      if (serverUrl.isEmpty()) {
        AILog.e(TAG, "Server URL not set, cannot connect.")
        updateState(ConnectionStatus.ERROR)
        return
      }

      AILog.i(TAG, "Connecting to ASR service: $serverUrl")
      updateState(ConnectionStatus.CONNECTING)

      launch {
        try {
          // 在协程中执行网络操作
          okHttpClient = OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .pingInterval(PING_INTERVAL, TimeUnit.SECONDS)
            .build()

          val request = Request.Builder().url(serverUrl).build()

          AILog.i(TAG, "Creating WebSocket connection...")
          webSocket = okHttpClient?.newWebSocket(request, createWebSocketListener())

          if (webSocket == null) {
            AILog.e(TAG, "Failed to create WebSocket, client is null.")
            handleConnectionFailure()
          }
        } catch (e: Exception) {
          AILog.e(TAG, "Error initializing connection", e)
          handleConnectionFailure()
        }
      }
    }
  }

  /**
   * 关闭客户端协程作用域
   */
  fun shutdown(reason: String = "AsrSocketClient shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "ASR socket client scope shut down: $reason")
  }

  override fun disconnect() {
    synchronized(stateLock) {
      if (state == ConnectionStatus.DISCONNECTED) {
        AILog.i(TAG, "Already disconnected.")
        return
      }
      AILog.i(TAG, "Initiating disconnect from ASR service...")
      updateState(ConnectionStatus.DISCONNECTING)
    }

    // 异步执行清理
    launch {
      try {
        cleanupNetworkResourcesSync()
        synchronized(stateLock) {
          updateState(ConnectionStatus.DISCONNECTED)
        }
        AILog.i(TAG, "Disconnected successfully.")
      } catch (e: Exception) {
        AILog.e(TAG, "Error during disconnect", e)
        synchronized(stateLock) {
          updateState(ConnectionStatus.ERROR)
        }
      }
    }
  }

  override suspend fun disconnectAndJoin() {
    synchronized(stateLock) {
      if (state == ConnectionStatus.DISCONNECTED) {
        AILog.i(TAG, "Already disconnected.")
        return
      }
      AILog.i(TAG, "Disconnecting from ASR service and waiting...")
      updateState(ConnectionStatus.DISCONNECTING)
    }

    // 同步执行清理并等待
    cleanupNetworkResources() // 只清理网络资源，不触碰协程作用域

    synchronized(stateLock) {
      updateState(ConnectionStatus.DISCONNECTED)
    }
    AILog.i(TAG, "Disconnected successfully.")
  }

  override fun destroy() {
    AILog.i(TAG, "Initiating client destroy...")

    val needsCleanup = synchronized(stateLock) {
      if (state != ConnectionStatus.DISCONNECTED) {
        updateState(ConnectionStatus.DISCONNECTING)
        true
      } else {
        false
      }
    }

    if (needsCleanup) {
      // 异步清理网络资源
      launch {
        try {
          cleanupNetworkResourcesSync()
          synchronized(stateLock) {
            updateState(ConnectionStatus.DISCONNECTED)
          }
          AILog.i(TAG, "Client destroyed successfully.")
        } catch (e: Exception) {
          AILog.e(TAG, "Error during client destroy", e)
        }
      }
    }

    // 销毁时才关闭协程作用域
    shutdown("Client destroy")
  }

  override suspend fun destroyAndJoin() {
    AILog.i(TAG, "Destroying client and waiting...")

    val needsCleanup = synchronized(stateLock) {
      if (state != ConnectionStatus.DISCONNECTED) {
        updateState(ConnectionStatus.DISCONNECTING)
        true
      } else {
        false
      }
    }

    if (needsCleanup) {
      // 同步清理网络资源并等待
      cleanupNetworkResources()
      synchronized(stateLock) {
        updateState(ConnectionStatus.DISCONNECTED)
      }
    }

    // 销毁时才关闭协程作用域
    shutdown("Client destroy")
    AILog.i(TAG, "Client destroyed successfully.")
  }

  private fun createWebSocketListener(): WebSocketListener {
    return object : WebSocketListener() {
      override fun onOpen(webSocket: WebSocket, response: Response) {
        AILog.i(TAG, "WebSocket connected.")
        synchronized(stateLock) {
          updateState(ConnectionStatus.CONNECTED)
        }
      }

      override fun onMessage(webSocket: WebSocket, text: String) {
        AILog.d(TAG, "Received text message: $text")
        launch {
          _transcriptionResultFlow.emit(text)
        }
      }

      override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
        AILog.i(TAG, "WebSocket closing: $code $reason")
        synchronized(stateLock) {
          updateState(ConnectionStatus.DISCONNECTING)
        }
      }

      override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
        AILog.i(TAG, "WebSocket closed: $code $reason")
        synchronized(stateLock) {
          // 只有在非主动断开的情况下才处理连接失败
          if (state != ConnectionStatus.DISCONNECTED && state != ConnectionStatus.DISCONNECTING) {
            handleConnectionFailure()
          } else {
            AILog.i(TAG, "WebSocket closed normally during disconnect process, no reconnection needed.")
          }
        }
      }

      override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
        AILog.e(TAG, "WebSocket error", t)
        handleConnectionFailure()
      }
    }
  }

  private fun handleConnectionFailure() {
    synchronized(stateLock) {
      // 如果是主动断开或正在断开，则不重连
      if (state == ConnectionStatus.DISCONNECTED || state == ConnectionStatus.DISCONNECTING) {
        AILog.i(TAG, "Connection closed during normal disconnect process, no reconnection needed.")
        return
      }

      AILog.w(TAG, "Connection failed or closed unexpectedly. Current state: $state")
      updateState(ConnectionStatus.ERROR)
    }

    // 只清理网络资源，不自动重连
    // 让 OnlineAsrOrchestrator 决定是否需要续传重连
    launch {
      try {
        // 清理网络资源，保留协程作用域
        cleanupNetworkResources()
        AILog.i(TAG, "Network resources cleaned up after connection failure. Waiting for OnlineAsrOrchestrator to handle resume AILogic.")
      } catch (e: Exception) {
        AILog.e(TAG, "Error during connection failure cleanup", e)
      }
    }
  }

  /**
   * 只清理网络资源，不触碰协程作用域 (异步版本)
   * 用于断开连接但保持客户端可重连的场景
   */
  private suspend fun cleanupNetworkResources() {
    AILog.d(TAG, "Cleaning up network resources...")

    // 直接关闭 WebSocket 连接
    webSocket?.close(1000, "Client cleanup")
    webSocket = null

    // OkHttp 建议使用 shutdownNow 来立即停止所有任务
    okHttpClient?.dispatcher?.executorService?.shutdownNow()
    okHttpClient?.connectionPool?.evictAll()
    okHttpClient = null

    AILog.d(TAG, "Network resources cleanup completed")
  }

  /**
   * 只清理网络资源，不触碰协程作用域 (同步版本)
   * 用于快速清理的场景
   */
  private fun cleanupNetworkResourcesSync() {
    AILog.d(TAG, "Cleaning up network resources (sync)...")

    // 直接关闭 WebSocket 连接
    webSocket?.close(1000, "Client cleanup")
    webSocket = null

    // OkHttp 建议使用 shutdownNow 来立即停止所有任务
    okHttpClient?.dispatcher?.executorService?.shutdownNow()
    okHttpClient?.connectionPool?.evictAll()
    okHttpClient = null

    AILog.d(TAG, "Network resources cleanup completed (sync)")
  }

  private fun updateState(newState: ConnectionStatus) {
    synchronized(stateLock) {
      if (state != newState) {
        state = newState
        _connectionStatusFlow.tryEmit(newState)
        AILog.i(TAG, "State changed to: $newState")
      }
    }
  }
}

/**
 * 连接状态枚举
 */
enum class ConnectionStatus {
  DISCONNECTED,
  CONNECTING,
  CONNECTED,
  DISCONNECTING,
  ERROR
}

